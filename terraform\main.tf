terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    archive = {
      source  = "hashicorp/archive"
      version = "~> 2.0"
    }
  }
}

provider "aws" {
  region = var.aws_region
}

# Data sources
data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

# Create deployment package
data "archive_file" "lambda_zip" {
  type        = "zip"
  source_file = "../lambda_deployment.py"
  output_path = "lambda_deployment.zip"
}

# S3 bucket for reports
resource "aws_s3_bucket" "security_reports" {
  bucket = "${var.report_bucket_prefix}-${data.aws_caller_identity.current.account_id}"
}

resource "aws_s3_bucket_encryption_configuration" "security_reports" {
  bucket = aws_s3_bucket.security_reports.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

resource "aws_s3_bucket_public_access_block" "security_reports" {
  bucket = aws_s3_bucket.security_reports.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_lifecycle_configuration" "security_reports" {
  bucket = aws_s3_bucket.security_reports.id

  rule {
    id     = "delete_old_reports"
    status = "Enabled"

    expiration {
      days = 90
    }
  }
}

# SNS Topic for notifications
resource "aws_sns_topic" "security_audit" {
  count = var.notification_email != "" ? 1 : 0
  name  = "aws-security-audit-notifications"
}

resource "aws_sns_topic_subscription" "security_audit_email" {
  count     = var.notification_email != "" ? 1 : 0
  topic_arn = aws_sns_topic.security_audit[0].arn
  protocol  = "email"
  endpoint  = var.notification_email
}

# IAM role for Lambda
resource "aws_iam_role" "security_audit_lambda" {
  name = "SecurityAuditLambdaRole"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "lambda_basic_execution" {
  role       = aws_iam_role.security_audit_lambda.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

resource "aws_iam_role_policy" "security_audit_policy" {
  name = "SecurityAuditPolicy"
  role = aws_iam_role.security_audit_lambda.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "iam:ListUsers",
          "iam:ListMFADevices"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "cloudtrail:DescribeTrails"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "s3:ListAllMyBuckets",
          "s3:GetBucketAcl",
          "s3:GetBucketEncryption"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "s3:PutObject",
          "s3:PutObjectAcl"
        ]
        Resource = "${aws_s3_bucket.security_reports.arn}/*"
      },
      {
        Effect = "Allow"
        Action = [
          "config:DescribeConfigurationRecorderStatus"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "guardduty:ListDetectors"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "sns:Publish"
        ]
        Resource = var.notification_email != "" ? aws_sns_topic.security_audit[0].arn : ""
      }
    ]
  })
}

# Lambda function
resource "aws_lambda_function" "security_audit" {
  filename         = data.archive_file.lambda_zip.output_path
  function_name    = "aws-security-controls-fetcher"
  role            = aws_iam_role.security_audit_lambda.arn
  handler         = "lambda_deployment.lambda_handler"
  runtime         = "python3.9"
  timeout         = 300
  source_code_hash = data.archive_file.lambda_zip.output_base64sha256

  environment {
    variables = {
      REPORT_BUCKET  = aws_s3_bucket.security_reports.bucket
      SNS_TOPIC_ARN  = var.notification_email != "" ? aws_sns_topic.security_audit[0].arn : ""
    }
  }
}

# CloudWatch Events Rule for scheduling
resource "aws_cloudwatch_event_rule" "security_audit_schedule" {
  name                = "security-audit-schedule"
  description         = "Schedule for AWS security controls audit"
  schedule_expression = var.schedule_expression
}

resource "aws_cloudwatch_event_target" "lambda_target" {
  rule      = aws_cloudwatch_event_rule.security_audit_schedule.name
  target_id = "SecurityAuditLambdaTarget"
  arn       = aws_lambda_function.security_audit.arn
}

resource "aws_lambda_permission" "allow_cloudwatch" {
  statement_id  = "AllowExecutionFromCloudWatch"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.security_audit.function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.security_audit_schedule.arn
}
