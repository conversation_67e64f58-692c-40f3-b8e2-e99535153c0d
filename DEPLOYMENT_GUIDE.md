# AWS Security Controls Fetcher - Deployment Guide

This guide provides step-by-step instructions for deploying the AWS Security Controls Fetcher into a client's AWS infrastructure.

## Prerequisites

### 1. AWS Account Access
- AWS account with administrative privileges
- AWS CLI installed and configured
- Appropriate IAM permissions for deployment

### 2. Required Tools
- **AWS CLI** v2.x or later
- **Python** 3.9 or later (for Lambda deployment)
- **Terraform** v1.0+ (if using Terraform deployment)
- **Git** (for cloning repository)

### 3. IAM Permissions for Deployment User

The user deploying this solution needs the following permissions:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "cloudformation:*",
                "lambda:*",
                "iam:*",
                "s3:*",
                "sns:*",
                "events:*",
                "logs:*"
            ],
            "Resource": "*"
        }
    ]
}
```

## Deployment Methods

### Method 1: CloudFormation (Recommended)

#### Step 1: Prepare Environment
```bash
# Clone or download the project
git clone <repository-url>
cd aws-security-controls-fetcher

# Verify AWS CLI configuration
aws sts get-caller-identity
```

#### Step 2: Configure Deployment
```bash
# Make deployment script executable
chmod +x deploy.sh

# Review and customize parameters in deploy.sh if needed
# - REGION: Target AWS region
# - BUCKET_PREFIX: S3 bucket name prefix
# - NOTIFICATION_EMAIL: Email for alerts (optional)
```

#### Step 3: Deploy
```bash
# Deploy with email notifications
./deploy.sh --region us-east-1 --email <EMAIL>

# Deploy without notifications
./deploy.sh --region us-east-1

# Deploy with custom stack name
./deploy.sh --region us-east-1 --stack-name custom-security-audit
```

#### Step 4: Verify Deployment
```bash
# Check CloudFormation stack status
aws cloudformation describe-stacks --stack-name aws-security-controls-fetcher

# Test Lambda function
aws lambda invoke --function-name aws-security-controls-fetcher response.json
cat response.json
```

### Method 2: Terraform

#### Step 1: Initialize Terraform
```bash
cd terraform

# Initialize Terraform
terraform init

# Copy and customize variables
cp terraform.tfvars.example terraform.tfvars
# Edit terraform.tfvars with your specific values
```

#### Step 2: Plan and Apply
```bash
# Review deployment plan
terraform plan

# Apply configuration
terraform apply

# Confirm with 'yes' when prompted
```

#### Step 3: Verify Deployment
```bash
# Show outputs
terraform output

# Test Lambda function
aws lambda invoke --function-name aws-security-controls-fetcher response.json
```

### Method 3: Manual EC2 Deployment

#### Step 1: Launch EC2 Instance
```bash
# Launch Amazon Linux 2 instance with appropriate IAM role
# Ensure the instance has the security audit IAM permissions
```

#### Step 2: Install Dependencies
```bash
# Connect to instance
ssh -i your-key.pem ec2-user@instance-ip

# Install Python and pip
sudo yum update -y
sudo yum install python3 python3-pip -y

# Install AWS CLI
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install
```

#### Step 3: Deploy Application
```bash
# Clone repository
git clone <repository-url>
cd aws-security-controls-fetcher

# Install Python dependencies
pip3 install -r requirements.txt

# Configure AWS credentials (if not using IAM role)
aws configure

# Test execution
python3 aws_controls_collector.py
```

#### Step 4: Setup Automation (Optional)
```bash
# Create cron job for weekly execution
crontab -e

# Add line for weekly execution (Sundays at 9 AM):
0 9 * * 0 /usr/bin/python3 /home/<USER>/aws-security-controls-fetcher/aws_controls_collector.py
```

## Post-Deployment Configuration

### 1. Verify SNS Subscription
If you provided an email address:
```bash
# Check SNS subscription status
aws sns list-subscriptions-by-topic --topic-arn <topic-arn>

# Confirm subscription via email
# Check your email and click the confirmation link
```

### 2. Test Scheduled Execution
```bash
# Manually trigger CloudWatch Events rule
aws events put-events --entries Source=manual,DetailType=test,Detail='{}'

# Check CloudWatch logs
aws logs describe-log-groups --log-group-name-prefix "/aws/lambda/aws-security-controls-fetcher"
```

### 3. Review S3 Bucket
```bash
# List reports bucket contents
aws s3 ls s3://aws-security-reports-<account-id>/

# Download a sample report
aws s3 cp s3://aws-security-reports-<account-id>/security-reports/latest.json ./
```

## Customization Options

### 1. Modify Audit Schedule
```bash
# Update CloudFormation parameter
aws cloudformation update-stack \
    --stack-name aws-security-controls-fetcher \
    --use-previous-template \
    --parameters ParameterKey=ScheduleExpression,ParameterValue="rate(1 day)"
```

### 2. Add Custom Security Checks
Edit `lambda_deployment.py` to add new security controls:
```python
def get_custom_security_check():
    # Add your custom security logic here
    return {"custom_check": True}

# Add to main report in lambda_handler
report["custom"] = get_custom_security_check()
```

### 3. Modify Report Format
Customize the report structure in the `lambda_handler` function to match your organization's requirements.

## Monitoring and Maintenance

### 1. CloudWatch Monitoring
```bash
# Create CloudWatch alarm for Lambda errors
aws cloudwatch put-metric-alarm \
    --alarm-name "SecurityAuditLambdaErrors" \
    --alarm-description "Alert on Lambda function errors" \
    --metric-name Errors \
    --namespace AWS/Lambda \
    --statistic Sum \
    --period 300 \
    --threshold 1 \
    --comparison-operator GreaterThanOrEqualToThreshold \
    --dimensions Name=FunctionName,Value=aws-security-controls-fetcher
```

### 2. Regular Updates
```bash
# Update Lambda function code
./deploy.sh --region us-east-1  # Re-run deployment script

# Or update via AWS CLI
aws lambda update-function-code \
    --function-name aws-security-controls-fetcher \
    --zip-file fileb://lambda-deployment.zip
```

### 3. Log Analysis
```bash
# View recent Lambda logs
aws logs filter-log-events \
    --log-group-name "/aws/lambda/aws-security-controls-fetcher" \
    --start-time $(date -d '1 hour ago' +%s)000
```

## Troubleshooting

### Common Issues

1. **Permission Denied Errors**
   - Verify IAM role has all required permissions
   - Check CloudTrail logs for specific permission failures

2. **Lambda Timeout**
   - Increase timeout in CloudFormation template
   - Optimize code for large AWS accounts

3. **S3 Access Denied**
   - Verify bucket policy and IAM permissions
   - Check bucket encryption settings

4. **SNS Delivery Failures**
   - Confirm email subscription
   - Check SNS topic permissions

### Debug Commands
```bash
# Check Lambda function configuration
aws lambda get-function --function-name aws-security-controls-fetcher

# View CloudFormation events
aws cloudformation describe-stack-events --stack-name aws-security-controls-fetcher

# Test specific AWS service access
aws iam list-users  # Test IAM access
aws s3 ls          # Test S3 access
```

## Security Considerations

1. **Least Privilege**: The solution uses minimal required permissions
2. **Encryption**: All data is encrypted in transit and at rest
3. **Access Logging**: All API calls are logged via CloudTrail
4. **Network Security**: Lambda runs in AWS managed infrastructure
5. **Data Retention**: Reports auto-delete after 90 days

## Cost Optimization

- **Lambda**: Use ARM-based runtime for cost savings
- **S3**: Enable Intelligent Tiering for long-term storage
- **CloudWatch**: Set log retention policies
- **SNS**: Use SMS only for critical alerts

## Support

For issues or questions:
1. Check CloudWatch logs for error details
2. Review AWS service quotas and limits
3. Verify IAM permissions and policies
4. Test individual AWS service APIs manually
