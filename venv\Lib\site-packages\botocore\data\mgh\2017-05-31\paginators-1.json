{"pagination": {"ListCreatedArtifacts": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "CreatedArtifactList"}, "ListDiscoveredResources": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "DiscoveredResourceList"}, "ListMigrationTasks": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "MigrationTaskSummaryList"}, "ListProgressUpdateStreams": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "ProgressUpdateStreamSummaryList"}, "ListApplicationStates": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "ApplicationStateList"}, "ListMigrationTaskUpdates": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "MigrationTaskUpdateList"}, "ListSourceResources": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "SourceResourceList"}}}