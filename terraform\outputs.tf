output "lambda_function_arn" {
  description = "ARN of the Security Audit Lambda function"
  value       = aws_lambda_function.security_audit.arn
}

output "lambda_function_name" {
  description = "Name of the Security Audit Lambda function"
  value       = aws_lambda_function.security_audit.function_name
}

output "reports_bucket_name" {
  description = "Name of the S3 bucket storing security reports"
  value       = aws_s3_bucket.security_reports.bucket
}

output "reports_bucket_arn" {
  description = "ARN of the S3 bucket storing security reports"
  value       = aws_s3_bucket.security_reports.arn
}

output "sns_topic_arn" {
  description = "ARN of the SNS topic for notifications"
  value       = var.notification_email != "" ? aws_sns_topic.security_audit[0].arn : null
}

output "cloudwatch_rule_name" {
  description = "Name of the CloudWatch Events rule for scheduling"
  value       = aws_cloudwatch_event_rule.security_audit_schedule.name
}

output "deployment_region" {
  description = "AWS region where resources are deployed"
  value       = data.aws_region.current.name
}

output "account_id" {
  description = "AWS Account ID where resources are deployed"
  value       = data.aws_caller_identity.current.account_id
}
