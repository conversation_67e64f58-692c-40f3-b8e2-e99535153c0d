# Example Terraform variables file
# Copy this to terraform.tfvars and customize for your deployment

# AWS region for deployment
aws_region = "us-east-1"

# Prefix for S3 bucket name (will be suffixed with account ID)
report_bucket_prefix = "aws-security-reports"

# Email for notifications (leave empty to disable notifications)
notification_email = "<EMAIL>"

# Schedule for automated audits
# Examples:
# - "rate(7 days)" - Weekly
# - "rate(1 day)" - Daily  
# - "cron(0 9 * * ? *)" - Daily at 9 AM UTC
# - "cron(0 9 ? * MON *)" - Every Monday at 9 AM UTC
schedule_expression = "rate(7 days)"
