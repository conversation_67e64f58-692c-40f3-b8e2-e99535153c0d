#!/bin/bash

# AWS Security Controls Fetcher Deployment Script
# This script deploys the security audit tool to AWS Lambda

set -e

# Configuration
STACK_NAME="aws-security-controls-fetcher"
REGION="us-east-1"
BUCKET_PREFIX="aws-security-reports"
NOTIFICATION_EMAIL=""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if AWS CLI is configured
check_aws_cli() {
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI is not installed. Please install it first."
        exit 1
    fi
    
    if ! aws sts get-caller-identity &> /dev/null; then
        print_error "AWS CLI is not configured. Please run 'aws configure' first."
        exit 1
    fi
    
    print_status "AWS CLI is configured"
}

# Function to create deployment package
create_deployment_package() {
    print_status "Creating Lambda deployment package..."
    
    # Create temporary directory
    TEMP_DIR=$(mktemp -d)
    
    # Copy Lambda function code
    cp lambda_deployment.py "$TEMP_DIR/lambda_function.py"
    
    # Create ZIP package
    cd "$TEMP_DIR"
    zip -r ../lambda-deployment.zip .
    cd - > /dev/null
    
    # Move ZIP to current directory
    mv "$TEMP_DIR/../lambda-deployment.zip" ./lambda-deployment.zip
    
    # Cleanup
    rm -rf "$TEMP_DIR"
    
    print_status "Deployment package created: lambda-deployment.zip"
}

# Function to upload Lambda code to S3
upload_lambda_code() {
    local deployment_bucket="$1"
    
    print_status "Uploading Lambda deployment package to S3..."
    
    aws s3 cp lambda-deployment.zip "s3://$deployment_bucket/lambda-deployment.zip" \
        --region "$REGION"
    
    print_status "Lambda code uploaded to S3"
}

# Function to deploy CloudFormation stack
deploy_stack() {
    local deployment_bucket="$1"
    
    print_status "Deploying CloudFormation stack..."
    
    # Prepare parameters
    PARAMETERS="ParameterKey=ReportBucketName,ParameterValue=$BUCKET_PREFIX"
    
    if [ -n "$NOTIFICATION_EMAIL" ]; then
        PARAMETERS="$PARAMETERS ParameterKey=NotificationEmail,ParameterValue=$NOTIFICATION_EMAIL"
    fi
    
    # Deploy stack
    aws cloudformation deploy \
        --template-file cloudformation_template.yaml \
        --stack-name "$STACK_NAME" \
        --parameter-overrides $PARAMETERS \
        --capabilities CAPABILITY_NAMED_IAM \
        --region "$REGION"
    
    print_status "CloudFormation stack deployed successfully"
}

# Function to update Lambda function code
update_lambda_code() {
    print_status "Updating Lambda function code..."
    
    aws lambda update-function-code \
        --function-name "aws-security-controls-fetcher" \
        --zip-file fileb://lambda-deployment.zip \
        --region "$REGION"
    
    print_status "Lambda function code updated"
}

# Function to test the deployment
test_deployment() {
    print_status "Testing Lambda function..."
    
    aws lambda invoke \
        --function-name "aws-security-controls-fetcher" \
        --region "$REGION" \
        --payload '{}' \
        response.json
    
    if [ $? -eq 0 ]; then
        print_status "Test invocation successful"
        cat response.json
        rm response.json
    else
        print_error "Test invocation failed"
        exit 1
    fi
}

# Main deployment function
main() {
    print_status "Starting AWS Security Controls Fetcher deployment..."
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --region)
                REGION="$2"
                shift 2
                ;;
            --email)
                NOTIFICATION_EMAIL="$2"
                shift 2
                ;;
            --stack-name)
                STACK_NAME="$2"
                shift 2
                ;;
            --help)
                echo "Usage: $0 [OPTIONS]"
                echo "Options:"
                echo "  --region REGION          AWS region (default: us-east-1)"
                echo "  --email EMAIL           Email for notifications (optional)"
                echo "  --stack-name NAME       CloudFormation stack name"
                echo "  --help                  Show this help message"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Check prerequisites
    check_aws_cli
    
    # Get AWS account ID
    ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
    print_status "Deploying to AWS Account: $ACCOUNT_ID"
    
    # Create deployment package
    create_deployment_package
    
    # Deploy CloudFormation stack
    deploy_stack
    
    # Update Lambda function code
    update_lambda_code
    
    # Test deployment
    test_deployment
    
    # Cleanup
    rm lambda-deployment.zip
    
    print_status "Deployment completed successfully!"
    print_status "Stack Name: $STACK_NAME"
    print_status "Region: $REGION"
    
    if [ -n "$NOTIFICATION_EMAIL" ]; then
        print_status "Notifications will be sent to: $NOTIFICATION_EMAIL"
    fi
    
    print_status "You can manually invoke the function or wait for the scheduled execution."
}

# Run main function
main "$@"
