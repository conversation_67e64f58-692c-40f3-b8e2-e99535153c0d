variable "aws_region" {
  description = "AWS region for deployment"
  type        = string
  default     = "us-east-1"
}

variable "report_bucket_prefix" {
  description = "Prefix for the S3 bucket name that will store security reports"
  type        = string
  default     = "aws-security-reports"
}

variable "notification_email" {
  description = "Email address for security audit notifications (optional)"
  type        = string
  default     = ""
}

variable "schedule_expression" {
  description = "CloudWatch Events schedule expression for automated audits"
  type        = string
  default     = "rate(7 days)"
  
  validation {
    condition = can(regex("^(rate\\(.*\\)|cron\\(.*\\))$", var.schedule_expression))
    error_message = "Schedule expression must be a valid rate() or cron() expression."
  }
}
