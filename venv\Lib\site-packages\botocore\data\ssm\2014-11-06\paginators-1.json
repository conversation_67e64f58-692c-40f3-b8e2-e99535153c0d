{"pagination": {"ListAssociations": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Associations"}, "ListCommandInvocations": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "CommandInvocations"}, "ListCommands": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Commands"}, "ListDocuments": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "DocumentIdentifiers"}, "DescribeInstanceInformation": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "InstanceInformationList"}, "DescribeActivations": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "ActivationList"}, "DescribeParameters": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Parameters"}, "DescribeAssociationExecutions": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "AssociationExecutions"}, "DescribeAssociationExecutionTargets": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "AssociationExecutionTargets"}, "GetInventory": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Entities"}, "GetParametersByPath": {"result_key": "Parameters", "output_token": "NextToken", "input_token": "NextToken", "limit_key": "MaxResults"}, "GetParameterHistory": {"result_key": "Parameters", "output_token": "NextToken", "input_token": "NextToken", "limit_key": "MaxResults"}, "DescribeAutomationExecutions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "AutomationExecutionMetadataList"}, "DescribeAutomationStepExecutions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "StepExecutions"}, "DescribeAvailablePatches": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "<PERSON><PERSON>"}, "DescribeEffectiveInstanceAssociations": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Associations"}, "DescribeEffectivePatchesForPatchBaseline": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "EffectivePatches"}, "DescribeInstanceAssociationsStatus": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "InstanceAssociationStatusInfos"}, "DescribeInstancePatchStates": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "InstancePatchStates"}, "DescribeInstancePatchStatesForPatchGroup": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "InstancePatchStates"}, "DescribeInstancePatches": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "<PERSON><PERSON>"}, "DescribeInventoryDeletions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "InventoryDeletions"}, "DescribeMaintenanceWindowExecutionTaskInvocations": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "WindowExecutionTaskInvocationIdentities"}, "DescribeMaintenanceWindowExecutionTasks": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "WindowExecutionTaskIdentities"}, "DescribeMaintenanceWindowExecutions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "WindowExecutions"}, "DescribeMaintenanceWindowSchedule": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "ScheduledWindowExecutions"}, "DescribeMaintenanceWindowTargets": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Targets"}, "DescribeMaintenanceWindowTasks": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Tasks"}, "DescribeMaintenanceWindows": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "WindowIdentities"}, "DescribeMaintenanceWindowsForTarget": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "WindowIdentities"}, "DescribePatchBaselines": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "BaselineIdentities"}, "DescribePatchGroups": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Mappings"}, "DescribeSessions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Sessions"}, "GetInventorySchema": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "<PERSON><PERSON><PERSON>"}, "ListAssociationVersions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "AssociationVersions"}, "ListComplianceItems": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "ComplianceItems"}, "ListComplianceSummaries": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "ComplianceSummaryItems"}, "ListDocumentVersions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "DocumentVersions"}, "ListResourceComplianceSummaries": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "ResourceComplianceSummaryItems"}, "ListResourceDataSync": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "ResourceDataSyncItems"}, "DescribeOpsItems": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "OpsItemSummaries"}, "DescribePatchProperties": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Properties"}, "GetOpsSummary": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Entities"}, "ListOpsItemEvents": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Summaries"}, "ListOpsMetadata": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "OpsMetadataList"}, "ListOpsItemRelatedItems": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Summaries"}, "GetResourcePolicies": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Policies"}, "DescribeInstanceProperties": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "InstanceProperties"}, "ListNodes": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Nodes"}, "ListNodesSummary": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Summary"}}}