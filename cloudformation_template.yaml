AWSTemplateFormatVersion: '2010-09-09'
Description: 'AWS Security Controls Fetcher - Lambda-based deployment'

Parameters:
  ReportBucketName:
    Type: String
    Default: aws-security-reports
    Description: S3 bucket name for storing security reports
  
  NotificationEmail:
    Type: String
    Description: Email address for security audit notifications
    Default: ""
  
  ScheduleExpression:
    Type: String
    Default: "rate(7 days)"
    Description: CloudWatch Events schedule expression (e.g., rate(7 days), cron(0 9 * * ? *))

Conditions:
  CreateNotification: !Not [!Equals [!Ref NotificationEmail, ""]]

Resources:
  # S3 Bucket for storing reports
  SecurityReportsBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Sub "${ReportBucketName}-${AWS::AccountId}"
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: AES256
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true
      LifecycleConfiguration:
        Rules:
          - Id: DeleteOldReports
            Status: Enabled
            ExpirationInDays: 90

  # SNS Topic for notifications
  SecurityAuditTopic:
    Type: AWS::SNS::Topic
    Condition: CreateNotification
    Properties:
      TopicName: aws-security-audit-notifications
      DisplayName: AWS Security Audit Notifications

  # SNS Subscription
  SecurityAuditSubscription:
    Type: AWS::SNS::Subscription
    Condition: CreateNotification
    Properties:
      TopicArn: !Ref SecurityAuditTopic
      Protocol: email
      Endpoint: !Ref NotificationEmail

  # IAM Role for Lambda
  SecurityAuditLambdaRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: SecurityAuditLambdaRole
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
      Policies:
        - PolicyName: SecurityAuditPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              # IAM permissions
              - Effect: Allow
                Action:
                  - iam:ListUsers
                  - iam:ListMFADevices
                Resource: "*"
              
              # CloudTrail permissions
              - Effect: Allow
                Action:
                  - cloudtrail:DescribeTrails
                Resource: "*"
              
              # S3 permissions for audit
              - Effect: Allow
                Action:
                  - s3:ListAllMyBuckets
                  - s3:GetBucketAcl
                  - s3:GetBucketEncryption
                Resource: "*"
              
              # S3 permissions for report storage
              - Effect: Allow
                Action:
                  - s3:PutObject
                  - s3:PutObjectAcl
                Resource: !Sub "${SecurityReportsBucket}/*"
              
              # Config permissions
              - Effect: Allow
                Action:
                  - config:DescribeConfigurationRecorderStatus
                Resource: "*"
              
              # GuardDuty permissions
              - Effect: Allow
                Action:
                  - guardduty:ListDetectors
                Resource: "*"
              
              # SNS permissions
              - Effect: Allow
                Action:
                  - sns:Publish
                Resource: !If [CreateNotification, !Ref SecurityAuditTopic, !Ref "AWS::NoValue"]

  # Lambda Function
  SecurityAuditLambda:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: aws-security-controls-fetcher
      Runtime: python3.9
      Handler: lambda_function.lambda_handler
      Role: !GetAtt SecurityAuditLambdaRole.Arn
      Timeout: 300
      Environment:
        Variables:
          REPORT_BUCKET: !Ref SecurityReportsBucket
          SNS_TOPIC_ARN: !If [CreateNotification, !Ref SecurityAuditTopic, ""]
      Code:
        ZipFile: |
          # Placeholder - replace with actual lambda_deployment.py content
          import json
          def lambda_handler(event, context):
              return {'statusCode': 200, 'body': json.dumps('Hello from Lambda!')}

  # CloudWatch Events Rule for scheduling
  SecurityAuditSchedule:
    Type: AWS::Events::Rule
    Properties:
      Name: security-audit-schedule
      Description: Schedule for AWS security controls audit
      ScheduleExpression: !Ref ScheduleExpression
      State: ENABLED
      Targets:
        - Arn: !GetAtt SecurityAuditLambda.Arn
          Id: SecurityAuditLambdaTarget

  # Permission for CloudWatch Events to invoke Lambda
  LambdaInvokePermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref SecurityAuditLambda
      Action: lambda:InvokeFunction
      Principal: events.amazonaws.com
      SourceArn: !GetAtt SecurityAuditSchedule.Arn

Outputs:
  LambdaFunctionArn:
    Description: ARN of the Security Audit Lambda function
    Value: !GetAtt SecurityAuditLambda.Arn
    Export:
      Name: !Sub "${AWS::StackName}-LambdaArn"

  ReportsBucketName:
    Description: Name of the S3 bucket storing security reports
    Value: !Ref SecurityReportsBucket
    Export:
      Name: !Sub "${AWS::StackName}-ReportsBucket"

  SNSTopicArn:
    Condition: CreateNotification
    Description: ARN of the SNS topic for notifications
    Value: !Ref SecurityAuditTopic
    Export:
      Name: !Sub "${AWS::StackName}-SNSTopic"
