import boto3
import json
import os
from datetime import datetime

def lambda_handler(event, context):
    """
    AWS Lambda handler for security controls audit
    """
    try:
        # Initialize report with timestamp
        report = {
            "timestamp": datetime.utcnow().isoformat(),
            "account_id": context.invoked_function_arn.split(':')[4],
            "region": os.environ['AWS_REGION'],
            "iam": get_iam_data(),
            "cloudtrail": get_cloudtrail_status(),
            "s3": get_s3_public_and_encryption(),
            "aws_config": get_config_status(),
            "guardduty": get_guardduty_status()
        }
        
        # Save to S3 if bucket is specified
        if 'REPORT_BUCKET' in os.environ:
            save_to_s3(report, os.environ['REPORT_BUCKET'])
        
        # Send to SNS if topic is specified
        if 'SNS_TOPIC_ARN' in os.environ:
            send_notification(report, os.environ['SNS_TOPIC_ARN'])
        
        return {
            'statusCode': 200,
            'body': json.dumps({
                'message': 'Security audit completed successfully',
                'report': report
            })
        }
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return {
            'statusCode': 500,
            'body': json.dumps({
                'error': str(e)
            })
        }

def get_iam_data():
    iam = boto3.client('iam')
    users = iam.list_users()['Users']
    user_data = []

    for user in users:
        mfa_devices = iam.list_mfa_devices(UserName=user['UserName'])['MFADevices']
        user_data.append({
            "username": user['UserName'],
            "mfa_enabled": len(mfa_devices) > 0
        })

    return {"iam_users": user_data}

def get_cloudtrail_status():
    ct = boto3.client('cloudtrail')
    trails = ct.describe_trails()['trailList']
    return {"cloudtrail_enabled": any(trail['IsMultiRegionTrail'] for trail in trails)}

def get_s3_public_and_encryption():
    s3 = boto3.client('s3')
    buckets = s3.list_buckets()['Buckets']
    result = []

    for bucket in buckets:
        name = bucket['Name']
        public = False
        encrypted = False

        try:
            acl = s3.get_bucket_acl(Bucket=name)
            for grant in acl['Grants']:
                if 'AllUsers' in grant['Grantee'].get('URI', ''):
                    public = True
        except Exception:
            pass

        try:
            enc = s3.get_bucket_encryption(Bucket=name)
            if enc:
                encrypted = True
        except:
            pass

        result.append({
            "bucket": name,
            "public": public,
            "encrypted": encrypted
        })

    return {"s3_buckets": result}

def get_config_status():
    config = boto3.client('config')
    try:
        status = config.describe_configuration_recorder_status()
        enabled = any(r['recording'] for r in status['ConfigurationRecordersStatus'])
        return {"config_enabled": enabled}
    except:
        return {"config_enabled": False}

def get_guardduty_status():
    gd = boto3.client('guardduty')
    detectors = gd.list_detectors()['DetectorIds']
    return {"guardduty_enabled": len(detectors) > 0}

def save_to_s3(report, bucket_name):
    """Save report to S3 bucket"""
    s3 = boto3.client('s3')
    timestamp = datetime.utcnow().strftime('%Y-%m-%d_%H-%M-%S')
    key = f"security-reports/aws-controls-report-{timestamp}.json"
    
    s3.put_object(
        Bucket=bucket_name,
        Key=key,
        Body=json.dumps(report, indent=2),
        ContentType='application/json'
    )
    print(f"Report saved to s3://{bucket_name}/{key}")

def send_notification(report, topic_arn):
    """Send notification via SNS"""
    sns = boto3.client('sns')
    
    # Create summary for notification
    summary = {
        "timestamp": report["timestamp"],
        "account_id": report["account_id"],
        "issues_found": []
    }
    
    # Check for security issues
    if not report["cloudtrail"]["cloudtrail_enabled"]:
        summary["issues_found"].append("CloudTrail not enabled")
    
    if not report["aws_config"]["config_enabled"]:
        summary["issues_found"].append("AWS Config not enabled")
    
    if not report["guardduty"]["guardduty_enabled"]:
        summary["issues_found"].append("GuardDuty not enabled")
    
    # Check for users without MFA
    users_without_mfa = [u for u in report["iam"]["iam_users"] if not u["mfa_enabled"]]
    if users_without_mfa:
        summary["issues_found"].append(f"{len(users_without_mfa)} users without MFA")
    
    # Check for public S3 buckets
    public_buckets = [b for b in report["s3"]["s3_buckets"] if b["public"]]
    if public_buckets:
        summary["issues_found"].append(f"{len(public_buckets)} public S3 buckets")
    
    message = f"AWS Security Audit Report\n\n{json.dumps(summary, indent=2)}"
    
    sns.publish(
        TopicArn=topic_arn,
        Message=message,
        Subject="AWS Security Controls Audit Report"
    )
    print(f"Notification sent to {topic_arn}")
