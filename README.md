# AWS Security Controls Fetcher

A comprehensive AWS security audit tool that assesses key security controls across your AWS infrastructure and generates detailed compliance reports.

## Features

- **IAM Security**: Checks MFA enablement for all users
- **CloudTrail Monitoring**: Verifies multi-region CloudTrail configuration
- **S3 Security**: Identifies public buckets and encryption status
- **AWS Config**: Monitors configuration recording status
- **GuardDuty**: Checks threat detection service status
- **Automated Reporting**: Generates JSON reports with timestamps
- **Notifications**: Optional email alerts via SNS
- **Scheduled Execution**: Configurable automated audits

## Deployment Options

### 1. AWS Lambda (Recommended)

Deploy as a serverless Lambda function with automated scheduling:

```bash
# Make deployment script executable
chmod +x deploy.sh

# Deploy with email notifications
./deploy.sh --region us-east-1 --email <EMAIL>

# Deploy without notifications
./deploy.sh --region us-east-1
```

#### Prerequisites for Lambda Deployment

1. **AWS CLI configured** with appropriate permissions
2. **IAM permissions** for the deploying user:
   - CloudFormation full access
   - Lambda full access
   - IAM role creation
   - S3 bucket creation
   - SNS topic creation
   - EventBridge rule creation

#### What Gets Deployed

- **Lambda Function**: Executes security audits
- **S3 Bucket**: Stores audit reports (encrypted, lifecycle managed)
- **IAM Role**: Minimal permissions for security scanning
- **CloudWatch Events**: Scheduled execution (weekly by default)
- **SNS Topic**: Optional email notifications
- **CloudFormation Stack**: Infrastructure as Code

### 2. EC2 Instance Deployment

For continuous monitoring or custom scheduling:

```bash
# Install dependencies
pip install -r requirements.txt

# Configure AWS credentials
aws configure

# Run manually
python aws_controls_collector.py

# Set up cron job for automation
crontab -e
# Add: 0 9 * * 1 /usr/bin/python3 /path/to/aws_controls_collector.py
```

### 3. Container Deployment

Using Docker for portable deployment:

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY aws_controls_collector.py .
CMD ["python", "aws_controls_collector.py"]
```

## Required AWS Permissions

The tool requires the following IAM permissions:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "iam:ListUsers",
                "iam:ListMFADevices",
                "cloudtrail:DescribeTrails",
                "s3:ListAllMyBuckets",
                "s3:GetBucketAcl",
                "s3:GetBucketEncryption",
                "config:DescribeConfigurationRecorderStatus",
                "guardduty:ListDetectors"
            ],
            "Resource": "*"
        }
    ]
}
```

## Configuration

### Environment Variables (Lambda)

- `REPORT_BUCKET`: S3 bucket for storing reports
- `SNS_TOPIC_ARN`: SNS topic for notifications
- `AWS_REGION`: AWS region for deployment

### Schedule Configuration

Modify the schedule in CloudFormation template:

```yaml
ScheduleExpression: "rate(7 days)"  # Weekly
# or
ScheduleExpression: "cron(0 9 * * ? *)"  # Daily at 9 AM UTC
```

## Output Format

The tool generates JSON reports with the following structure:

```json
{
  "timestamp": "2024-01-15T10:30:00.000Z",
  "account_id": "************",
  "region": "us-east-1",
  "iam": {
    "iam_users": [
      {
        "username": "user1",
        "mfa_enabled": true
      }
    ]
  },
  "cloudtrail": {
    "cloudtrail_enabled": true
  },
  "s3": {
    "s3_buckets": [
      {
        "bucket": "my-bucket",
        "public": false,
        "encrypted": true
      }
    ]
  },
  "aws_config": {
    "config_enabled": true
  },
  "guardduty": {
    "guardduty_enabled": true
  }
}
```

## Security Considerations

1. **Least Privilege**: The tool uses minimal required permissions
2. **Encryption**: Reports are stored encrypted in S3
3. **Access Logging**: All API calls are logged via CloudTrail
4. **Network Security**: Lambda runs in AWS managed VPC
5. **Data Retention**: Reports auto-delete after 90 days

## Monitoring and Alerting

- **CloudWatch Logs**: Function execution logs
- **CloudWatch Metrics**: Function performance metrics
- **SNS Notifications**: Email alerts for security issues
- **S3 Access Logs**: Report access tracking

## Troubleshooting

### Common Issues

1. **Permission Denied**: Ensure IAM role has required permissions
2. **Timeout**: Increase Lambda timeout for large AWS accounts
3. **Rate Limiting**: Add retry logic for API throttling

### Debugging

```bash
# Check CloudWatch logs
aws logs describe-log-groups --log-group-name-prefix "/aws/lambda/aws-security-controls-fetcher"

# Test Lambda function
aws lambda invoke --function-name aws-security-controls-fetcher response.json
```

## Cost Estimation

### Lambda Deployment (Typical)
- **Lambda**: ~$0.20/month (weekly execution)
- **S3 Storage**: ~$0.50/month (reports)
- **CloudWatch Logs**: ~$0.10/month
- **SNS**: ~$0.05/month (notifications)

**Total**: ~$0.85/month

## Support and Maintenance

- **Updates**: Use deployment script to update Lambda code
- **Monitoring**: Set up CloudWatch alarms for failures
- **Backup**: Reports are automatically stored in S3
- **Scaling**: Lambda automatically scales with account size

## License

This tool is provided as-is for security assessment purposes. Ensure compliance with your organization's security policies before deployment.
